<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { ReportApi } from '#/api/report/report';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Card, Table, Typography } from 'ant-design-vue';

interface Props {
  productAnalysisData?: ReportApi.ProductAnalysis;
}

const props = withDefaults(defineProps<Props>(), {
  productAnalysisData: () => ({
    mainPurchaseProducts: [],
    mainSalesProducts: [],
    conclusion: '',
  }),
});

const { Title, Paragraph } = Typography;

const inputChartRef = ref<EchartsUIType>();
const outputChartRef = ref<EchartsUIType>();

const { renderEcharts: renderInputChart } = useEcharts(inputChartRef);
const { renderEcharts: renderOutputChart } = useEcharts(outputChartRef);

// 表格列配置
const columns = [
  {
    title: '商品名称',
    dataIndex: 'name',
    key: 'name',
    width: 300,
    fixed: 'left' as const,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 80,
    align: 'center' as const,
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 120,
    align: 'right' as const,
    customRender: ({ text }: { text: number }) => text.toLocaleString(),
  },
  {
    title: '金额(元)',
    dataIndex: 'amount',
    key: 'amount',
    width: 150,
    align: 'right' as const,
    customRender: ({ text }: { text: number }) => text.toLocaleString(),
  },
  {
    title: '均价(元)',
    dataIndex: 'avgPrice',
    key: 'avgPrice',
    width: 120,
    align: 'right' as const,
    customRender: ({ text }: { text: number }) => text.toFixed(2),
  },
  {
    title: '占比(%)',
    dataIndex: 'percentage',
    key: 'percentage',
    width: 100,
    align: 'right' as const,
    customRender: ({ text }: { text: number }) => `${text.toFixed(2)}%`,
  },
];

// 计算图表数据
const purchaseChartData = computed(() => {
  const data = props.productAnalysisData?.mainPurchaseProducts || [];
  return {
    title: {
      text: '主要进项商品分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'item' as const,
      formatter: '{a} <br/>{b}: {c}万元 ({d}%)',
    },
    legend: {
      orient: 'vertical' as const,
      right: '10%',
      top: '15%',
    },
    series: [
      {
        name: '进项商品',
        type: 'pie' as const,
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        data: data.map((item: ReportApi.ProductItem) => ({
          value: Math.round(item.amount / 10_000),
          name: item.name,
        })),
      },
    ],
  };
});

const salesChartData = computed(() => {
  const data = props.productAnalysisData?.mainSalesProducts || [];
  return {
    title: {
      text: '主要销项商品分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'item' as const,
      formatter: '{a} <br/>{b}: {c}万元 ({d}%)',
    },
    legend: {
      orient: 'vertical' as const,
      right: '10%',
      top: '15%',
    },
    series: [
      {
        name: '销项商品',
        type: 'pie' as const,
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        data: data.map((item: ReportApi.ProductItem) => ({
          value: Math.round(item.amount / 10_000),
          name: item.name,
        })),
      },
    ],
  };
});

const renderCharts = () => {
  if (props.productAnalysisData?.mainPurchaseProducts?.length) {
    renderInputChart(purchaseChartData.value);
  }
  if (props.productAnalysisData?.mainSalesProducts?.length) {
    renderOutputChart(salesChartData.value);
  }
};

onMounted(() => {
  setTimeout(() => {
    renderCharts();
  }, 100);
});

watch(
  () => props.productAnalysisData,
  () => {
    setTimeout(() => {
      renderCharts();
    }, 100);
  },
  { deep: true },
);
</script>

<template>
  <div>
    <!-- 进项商品分析 -->
    <Card id="main_input_goods" class="mt-4" :bordered="false">
      <Title :level="4">1、主要进项商品</Title>

      <!-- 进项商品分布图表 -->
      <div class="mb-4">
        <div class="h-96 w-full">
          <EchartsUI ref="inputChartRef" height="400px" />
        </div>
      </div>

      <!-- 图例说明 -->
      <div class="mt-4 text-sm text-gray-600">
        <p><strong>图例说明：</strong></p>
        <p>
          比例 = 当前商品含税金额 / 全部商品含税金额 * 100%，四舍五入取整数。
        </p>
        <p>仅显示前10项主要商品，详细分析进入【进项】或【销项】专项分析</p>
      </div>

      <Table
        :columns="columns"
        :data-source="productAnalysisData?.mainPurchaseProducts || []"
        :scroll="{ x: 1200 }"
        size="small"
        :pagination="{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }"
      />
    </Card>

    <!-- 销项商品分析 -->
    <Card id="main_output_goods" class="mt-4" :bordered="false">
      <Title :level="4">2、主要销项商品</Title>

      <!-- 销项商品分布图表 -->
      <div class="mb-4">
        <div class="h-96 w-full">
          <EchartsUI ref="outputChartRef" height="400px" />
        </div>
      </div>

      <!-- 图例说明 -->
      <div class="mt-4 text-sm text-gray-600">
        <p><strong>图例说明：</strong></p>
        <p>
          比例 = 当前商品含税金额 / 全部商品含税金额 * 100%，四舍五入取整数。
        </p>
        <p>仅显示前10项主要商品，详细分析进入【进项】或【销项】专项分析</p>
      </div>

      <Table
        :columns="columns"
        :data-source="productAnalysisData?.mainSalesProducts || []"
        :scroll="{ x: 1200 }"
        size="small"
        :pagination="{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }"
      />
    </Card>

    <!-- 分析结论 -->
    <Card id="analysis_conclusion" class="mt-4" :bordered="false">
      <Title :level="4">3、分析结论</Title>
      <div class="rounded-lg bg-gray-50 p-4">
        <Paragraph class="mb-0">
          {{ productAnalysisData?.conclusion }}
        </Paragraph>
      </div>
    </Card>
  </div>
</template>
