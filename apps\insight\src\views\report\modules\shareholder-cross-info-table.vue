<script lang="ts" setup>
import type { ReportApi } from '#/api/report/report';

import { computed } from 'vue';

import { Table, Typography } from 'ant-design-vue';

import { MENU_KEYS } from '#/views/report/config/report-menu-config';

interface Props {
  shareholderAnalysis?: ReportApi.BasicInfo['shareholderAnalysis'];
}

const props = withDefaults(defineProps<Props>(), {
  shareholderAnalysis: () => ({
    crossShareholdingCompanies: [],
    crossExecutivesCompanies: [],
    crossLegalRepresentativeCompanies: [],
  }),
});

const { Title } = Typography;

// 股东交叉持股表格列配置
const shareholdingColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center' as const,
    customRender: ({ index }: { index: number }) => index + 1,
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
  {
    title: '股东名称',
    dataIndex: 'shareholderName',
    key: 'shareholderName',
    align: 'left' as const,
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
  {
    title: '持股比例(%)',
    dataIndex: 'shareholdingRatio',
    key: 'shareholdingRatio',
    width: 150,
    align: 'right' as const,
    customRender: ({ text }: { text: number }) => `${text}%`,
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
];

// 高管交叉表格列配置
const executivesColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center' as const,
    customRender: ({ index }: { index: number }) => index + 1,
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
  {
    title: '公司名称',
    dataIndex: 'companyName',
    key: 'companyName',
    align: 'left' as const,
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
  {
    title: '职位',
    dataIndex: 'position',
    key: 'position',
    width: 150,
    align: 'center' as const,
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
];

// 法定代表人交叉表格列配置
const legalRepColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center' as const,
    customRender: ({ index }: { index: number }) => index + 1,
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
  {
    title: '公司名称',
    dataIndex: 'companyName',
    key: 'companyName',
    align: 'left' as const,
    customCell: () => ({
      style: {
        backgroundColor: '#f4f8ff',
        padding: '8px 12px',
      },
    }),
    customHeaderCell: () => ({
      style: {
        backgroundColor: '#dfe9fe',
        color: 'black',
        fontWeight: 'bold',
        padding: '10px 12px',
      },
    }),
  },
];

// 计算表格数据
const shareholdingData = computed(() => {
  return props.shareholderAnalysis?.crossShareholdingCompanies || [];
});

const executivesData = computed(() => {
  return props.shareholderAnalysis?.crossExecutivesCompanies || [];
});

const legalRepData = computed(() => {
  return props.shareholderAnalysis?.crossLegalRepresentativeCompanies || [];
});
</script>

<template>
  <div class="py-4">
    <!-- 股东交叉持股信息 -->
    <div :id="MENU_KEYS.SHAREHOLDER_INFORMATION" class="mb-6">
      <Title :level="5" class="mb-4">2.1 股东交叉持股企业</Title>
      <Table
        :columns="shareholdingColumns"
        :data-source="shareholdingData"
        :pagination="false"
        size="small"
        bordered
        :scroll="{ x: 400 }"
      />
    </div>

    <!-- 高管交叉信息 -->
    <div :id="MENU_KEYS.EXECUTIVE_CROSS_INFO" class="mb-6">
      <Title :level="5" class="mb-4">2.2 高管交叉任职企业</Title>
      <Table
        :columns="executivesColumns"
        :data-source="executivesData"
        :pagination="false"
        size="small"
        bordered
        :scroll="{ x: 400 }"
      />
    </div>

    <!-- 法定代表人交叉信息 -->
    <div :id="MENU_KEYS.LEGAL_PERSON_CROSS_INFO" class="mb-6">
      <Title :level="5" class="mb-4">2.3 法定代表人交叉任职企业</Title>
      <Table
        :columns="legalRepColumns"
        :data-source="legalRepData"
        :pagination="false"
        size="small"
        bordered
        :scroll="{ x: 400 }"
      />
    </div>
  </div>
</template>
