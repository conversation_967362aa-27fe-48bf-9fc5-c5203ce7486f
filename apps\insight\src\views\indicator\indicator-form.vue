<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';

import { ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import {
  createIndicatorConfig,
  getIndicatorCategoriesList,
  IndicatorApi,
  updateIndicatorConfig,
} from '#/api/indicator';

const emit = defineEmits<{
  success: [];
}>();

const formData = ref<IndicatorApi.IndicatorConfig | null>(null);
const level1Categories = ref<IndicatorApi.IndicatorCategory[]>([]);
const level2Categories = ref<IndicatorApi.IndicatorCategory[]>([]);
const level1Options = ref<{ label: string; value: number }[]>([]);
const level2Options = ref<{ label: string; value: number }[]>([]);
const categoriesLoaded = ref(false);

// 获取分类数据
async function loadCategories() {
  if (categoriesLoaded.value) {
    return;
  }

  try {
    const [level1Response, level2Response] = await Promise.all([
      getIndicatorCategoriesList({ level: 1, status: 1 }),
      getIndicatorCategoriesList({ level: 2, status: 1 }),
    ]);

    const level1Data = Array.isArray(level1Response) ? level1Response : [];
    const level2Data = Array.isArray(level2Response) ? level2Response : [];

    level1Categories.value = level1Data;
    level2Categories.value = level2Data;

    // 构建分类选项
    level1Options.value = level1Data.map((cat) => ({
      label: cat.name,
      value: cat.id,
    }));
    level2Options.value = level2Data.map((cat) => ({
      label: cat.name,
      value: cat.id,
    }));

    // 标记已加载
    categoriesLoaded.value = true;
  } catch (error) {
    console.error('获取分类数据失败:', error);
    level1Categories.value = [];
    level2Categories.value = [];
    level1Options.value = [];
    level2Options.value = [];
  }
}

// 重新加载分类数据
function reloadCategories() {
  categoriesLoaded.value = false;
  return loadCategories();
}

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  closeOnPressEscape: false,
  draggable: true,
  onOpened: async () => {
    const data = modalApi.getData<any>();
    formData.value = data;

    await loadCategories();

    if (data && data.id) {
      // 编辑模式，设置表单值
      const formValues = {
        class1: data.class1 || undefined,
        class2: data.class2 || undefined,
        indicatorCode: data.indicatorCode || '',
        indicatorName: data.indicatorName || '',
        executeSql: data.executeSql || '',
        returnType: data.returnType || 0,
        description: data.description || '',
        status: data.status === 1,
      };

      form.setValues(formValues);
    } else {
      // 新增模式，重置表单并设置默认值
      form.resetForm();
      const formValues = {
        class1: data?.class1 || undefined,
        class2: data?.class2 || undefined,
        indicatorCode: '',
        indicatorName: '',
        executeSql: '',
        returnType: data?.returnType || 0,
        description: '',
        status: true,
      };

      form.setValues(formValues);
    }
  },
});

/**
 * 表单配置
 */
const formOptions: VbenFormProps = {
  schema: [
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择一级分类',
        allowClear: true,
        options: level1Options,
      },
      fieldName: 'class1',
      label: '一级分类',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择二级分类',
        allowClear: true,
        options: level2Options,
      },
      fieldName: 'class2',
      label: '二级分类',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '指标编码',
      },
      fieldName: 'indicatorCode',
      label: '指标编码',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '指标名称',
      },
      fieldName: 'indicatorName',
      label: '指标名称',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '执行SQL语句',
        rows: 4,
      },
      fieldName: 'executeSql',
      label: '执行SQL',
      rules: 'required',
      formItemClass: 'sm:col-span-2',
    },
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择数据类型',
        options: IndicatorApi.RETURN_TYPE_OPTIONS,
      },
      fieldName: 'returnType',
      label: '数据类型',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedChildren: '启用',
        unCheckedChildren: '禁用',
        style: { width: '80px' },
      },
      fieldName: 'status',
      label: '状态',
      defaultValue: true,
    },

    {
      component: 'Textarea',
      componentProps: {
        placeholder: '指标描述',
        rows: 2,
      },
      fieldName: 'description',
      label: '指标描述',
      formItemClass: 'sm:col-span-2',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2',
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
};

const [Form, form] = useVbenForm(formOptions);

/**
 * 提交表单
 */
async function handleSubmit() {
  try {
    const validateResult = await form.validate();
    if (
      !validateResult ||
      (validateResult && !validateResult.valid) ||
      (validateResult &&
        validateResult.errors &&
        Object.keys(validateResult.errors).length > 0)
    ) {
      message.error('请填写所有必填项');
      return;
    }

    const values = await form.getValues();
    const requestData: IndicatorApi.CreateIndicatorConfigRequest = {
      class1: values.class1,
      class2: values.class2,
      indicatorCode: values.indicatorCode,
      indicatorName: values.indicatorName,
      executeSql: values.executeSql,
      returnType: values.returnType,
      description: values.description,
      status: values.status ? 1 : 0,
    };

    if (formData.value && formData.value.id) {
      // 编辑模式
      await updateIndicatorConfig({
        ...requestData,
        id: formData.value.id,
      });
      message.success('更新成功');
    } else {
      // 新增模式
      await createIndicatorConfig(requestData);
      message.success('创建成功');
    }

    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('保存指标配置失败:', error);
    message.error('保存指标配置失败');
  }
}

function handleCancel() {
  modalApi.close();
}

defineExpose({
  reloadCategories,
});
</script>

<template>
  <Modal
    :title="formData && formData.id ? '编辑指标配置' : '新增指标配置'"
    :width="900"
  >
    <Form />
    <template #footer>
      <div class="flex justify-end space-x-2">
        <Button @click="handleCancel"> 取消 </Button>
        <Button type="primary" @click="handleSubmit"> 保存 </Button>
      </div>
    </template>
  </Modal>
</template>
