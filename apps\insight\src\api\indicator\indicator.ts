import { requestClient } from '#/api/request';

export namespace IndicatorApi {
  /**
   * 指标数据类型枚举
   */
  export const RETURN_TYPE_OPTIONS = [
    { label: 'Bigdecimal', value: 0 },
    { label: 'Object', value: 1 },
    { label: 'List', value: 2 },
  ] as const;

  /**
   * 获取指标数据类型显示文本
   */
  export function getReturnTypeLabel(value: number): string {
    const option = RETURN_TYPE_OPTIONS.find((item) => item.value === value);
    return option ? option.label : '未知';
  }

  /**
   * 指标配置接口
   */
  export interface IndicatorConfig {
    /** 主键ID */
    id: number;
    /** 指标一级分类编号 */
    class1?: number;
    /** 指标二级分类编号 */
    class2?: number;
    /** 指标编码 */
    indicatorCode: string;
    /** 指标名称 */
    indicatorName: string;
    /** 执行sql */
    executeSql: string;
    /** 指标数据类型 */
    returnType: number;
    /** 指标描述 */
    description?: string;
    /** 状态(1-启用,0-禁用) */
    status: number;
    /** 创建时间 */
    createdAt: string;
    /** 更新时间 */
    updatedAt: string;
  }

  /**
   * 指标分类接口
   */
  export interface IndicatorCategory {
    /** 主键ID */
    id: number;
    /** 分类名称 */
    name: string;
    /** 级别（1,2,...） */
    level: number;
    /** 描述 */
    description?: string;
    /** 状态(1-启用,0-禁用) */
    status: number;
    /** 创建时间 */
    createdAt: string;
    /** 更新时间 */
    updatedAt: string;
    /** 子分类列表（用于树形结构） */
    children?: IndicatorCategory[];
    /** 该分类下的指标配置列表 */
    indicators?: IndicatorConfig[];
  }

  /**
   * 创建指标分类请求接口
   */
  export interface CreateIndicatorCategoryRequest {
    /** 分类名称 */
    name: string;
    /** 级别（1,2,...） */
    level: number;
    /** 描述 */
    description?: string;
    /** 状态(1-启用,0-禁用) */
    status: number;
  }

  /**
   * 更新指标分类请求接口
   */
  export interface UpdateIndicatorCategoryRequest
    extends CreateIndicatorCategoryRequest {
    /** 主键ID */
    id: number;
  }

  /**
   * 指标分类列表查询参数接口
   */
  export interface IndicatorCategoryListParams {
    /** 页码 */
    page?: number;
    /** 每页数量 */
    pageSize?: number;
    /** 分类名称 */
    name?: string;
    /** 级别 */
    level?: number;
    /** 状态 */
    status?: number;
  }

  /**
   * 创建指标配置请求接口
   */
  export interface CreateIndicatorConfigRequest {
    /** 指标一级分类编号 */
    class1?: number;
    /** 指标二级分类编号 */
    class2?: number;
    /** 指标编码 */
    indicatorCode: string;
    /** 指标名称 */
    indicatorName: string;
    /** 执行sql */
    executeSql: string;
    /** 指标数据类型 */
    returnType: number;
    /** 指标描述 */
    description?: string;
    /** 状态(1-启用,0-禁用) */
    status: number;
  }

  /**
   * 更新指标配置请求接口
   */
  export interface UpdateIndicatorConfigRequest
    extends CreateIndicatorConfigRequest {
    /** 主键ID */
    id: number;
  }

  /**
   * 指标配置列表查询参数接口
   */
  export interface IndicatorConfigListParams {
    /** 页码 */
    page?: number;
    /** 每页数量 */
    pageSize?: number;
    /** 指标一级分类编号 */
    class1?: number;
    /** 指标二级分类编号 */
    class2?: number;
    /** 指标编码 */
    indicatorCode?: string;
    /** 指标名称 */
    indicatorName?: string;
    /** 状态 */
    status?: number;
  }
}

/**
 * 获取指标配置分页列表
 */
async function getIndicatorConfigsPage(
  params: IndicatorApi.IndicatorConfigListParams,
) {
  const response = await requestClient.get<{
    records: IndicatorApi.IndicatorConfig[];
    total: number;
  }>('/indicator_config/page', { params });

  return {
    records: response.records,
    total: response.total,
  };
}

/**
 * 创建指标配置
 */
async function createIndicatorConfig(
  data: IndicatorApi.CreateIndicatorConfigRequest,
) {
  return requestClient.post('/indicator_config/add', data);
}

/**
 * 更新指标配置
 */
async function updateIndicatorConfig(
  data: IndicatorApi.UpdateIndicatorConfigRequest,
) {
  return requestClient.put(`/indicator_config/${data.id}`, data);
}

/**
 * 删除指标配置
 */
async function deleteIndicatorConfig(id: number) {
  return requestClient.delete(`/indicator_config/${id}`);
}

/**
 * 获取指标配置详情
 */
async function getIndicatorConfigDetail(id: number) {
  return requestClient.get<IndicatorApi.IndicatorConfig>(
    `/indicator_config/${id}`,
  );
}

/**
 * 获取指标分类列表
 */
async function getIndicatorCategoriesList(params?: {
  level?: number;
  status?: number;
}) {
  return requestClient.get<IndicatorApi.IndicatorCategory[]>(
    '/indicator_category/list',
    { params },
  );
}

/**
 * 创建指标分类
 */
async function createIndicatorCategory(
  data: IndicatorApi.CreateIndicatorCategoryRequest,
) {
  return requestClient.post('/indicator_category/add', data);
}

/**
 * 更新指标分类
 */
async function updateIndicatorCategory(
  data: IndicatorApi.UpdateIndicatorCategoryRequest,
) {
  return requestClient.put(`/indicator_category/${data.id}`, data);
}

/**
 * 删除指标分类
 */
async function deleteIndicatorCategory(id: number) {
  return requestClient.delete(`/indicator_category/${id}`);
}

/**
 * 获取指标分类详情
 */
async function getIndicatorCategoryDetail(id: number) {
  return requestClient.get<IndicatorApi.IndicatorCategory>(
    `/indicator_category/${id}`,
  );
}

/**
 * 获取指标分类及其下的指标配置
 */
async function getIndicatorCategoriesWithConfigs(
  params?: IndicatorApi.IndicatorCategoryListParams,
) {
  const response = await requestClient.get('/indicator_category/page', {
    params,
  });

  return {
    records: response.records,
    total: response.total || 0,
  };
}

export {
  createIndicatorCategory,
  createIndicatorConfig,
  deleteIndicatorCategory,
  deleteIndicatorConfig,
  getIndicatorCategoriesList,
  getIndicatorCategoriesWithConfigs,
  getIndicatorCategoryDetail,
  getIndicatorConfigDetail,
  getIndicatorConfigsPage,
  updateIndicatorCategory,
  updateIndicatorConfig,
};
